# Untitled - By: 26962 - 周四 7月 31 2025

import sensor, image, time, math, ustruct
from machine import UART

uart = UART(2, baudrate=460800)
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QQVGA)  # 160x120
sensor.set_framerate(40)
sensor.skip_frames(time=2000)
clock = time.clock()

# 旋转矩形识别参数
MIN_AREA = 1500
MAX_AREA = 9500
MIN_ASPECT_RATIO = 1.20
MAX_ASPECT_RATIO = 1.60
ANGLE_THRESHOLD = 38  # 允许的角度偏差(度)

# 透视矫正参数
ENABLE_PERSPECTIVE_CORRECTION = True    # 是否启用透视矫正
MIN_DISTORTION_THRESHOLD = 0.15         # 最小变形阈值，低于此值不进行矫正
CORRECTION_STRENGTH = 0.8               # 矫正强度系数 (0.0-1.0)
SHOW_CORRECTION_DEBUG = True            # 是否显示原始中心点和矫正中心点对比

# 稳定性增强参数
ENABLE_STABILITY_FILTER = True          # 启用稳定性滤波
DETECTION_THRESHOLD_BASE = 2000         # 基础检测阈值
THRESHOLD_ADAPT_SPEED = 0.1             # 阈值自适应速度
SMOOTHING_FACTOR = 0.7                  # 位置平滑系数 (0.0-1.0, 越大越平滑)
MIN_DETECTION_CONFIDENCE = 0.6          # 最小检测置信度
STABLE_FRAMES_REQUIRED = 3              # 需要连续稳定检测的帧数

# 帧头帧尾定义
FRAME_HEADER = 0xA1
FRAME_FOOTER = 0xAA

# PID控制变量
last_goal_bias_x = 0
bias_count_x = 0

# 稳定性滤波变量
last_valid_center = [80, 60]  # 上一次有效的中心点 (QQVGA中心)
last_valid_corners = None     # 上一次有效的角点
detection_threshold = DETECTION_THRESHOLD_BASE  # 动态检测阈值
stable_detection_count = 0    # 连续稳定检测计数
last_detection_confidence = 0.0  # 上一次检测置信度

def calculate_angle(p1, p2, p3):
    """计算三个点形成的角度"""
    # 计算向量
    v1 = (p1[0] - p2[0], p1[1] - p2[1])
    v2 = (p3[0] - p2[0], p3[1] - p2[1])

    # 计算点积
    dot_product = v1[0] * v2[0] + v1[1] * v2[1]

    # 计算模长
    mag_v1 = math.sqrt(v1[0]**2 + v1[1]**2)
    mag_v2 = math.sqrt(v2[0]**2 + v2[1]**2)

    # 计算角度(弧度)并转为度
    if mag_v1 * mag_v2 == 0:
        return 90  # 避免除以零错误
    angle_rad = math.acos(dot_product / (mag_v1 * mag_v2))
    angle_deg = math.degrees(angle_rad)

    return angle_deg

def is_valid_rectangle(corners):
    """检查是否是有效的矩形(角度接近90度)"""
    # 计算所有四个角的角度
    angles = [
        calculate_angle(corners[3], corners[0], corners[1]),
        calculate_angle(corners[0], corners[1], corners[2]),
        calculate_angle(corners[1], corners[2], corners[3]),
        calculate_angle(corners[2], corners[3], corners[0])
    ]

    # 检查角度是否接近90度
    valid_angles = 0
    for angle in angles:
        if abs(angle - 90) < ANGLE_THRESHOLD:
            valid_angles += 1

    return valid_angles >= 3  # 至少3个角接近90度

def calculate_distortion_simple(corners):
    """简化的变形程度计算，避免复杂运算"""
    # 计算四条边的长度
    side_lengths = []
    for i in range(4):
        p1 = corners[i]
        p2 = corners[(i + 1) % 4]
        # 使用简化的距离计算（避免开方运算）
        dx = p1[0] - p2[0]
        dy = p1[1] - p2[1]
        length_sq = dx * dx + dy * dy
        side_lengths.append(length_sq)

    # 计算长宽比（使用平方值避免开方）
    max_side_sq = max(side_lengths)
    min_side_sq = min(side_lengths)

    if min_side_sq == 0:
        return 1.0

    # 简化的变形评估
    ratio = max_side_sq / min_side_sq
    distortion = (ratio - 1.0) / ratio  # 归一化到0-1范围

    return distortion

def calculate_detection_confidence(rect_obj, corners):
    """计算检测置信度，用于稳定性评估"""
    # 基于面积稳定性
    area = rect_obj.w() * rect_obj.h()
    area_score = 1.0 if MIN_AREA <= area <= MAX_AREA else 0.5

    # 基于长宽比稳定性
    aspect_ratio = max(rect_obj.w(), rect_obj.h()) / min(rect_obj.w(), rect_obj.h())
    aspect_score = 1.0 if MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO else 0.7

    # 基于角度稳定性
    angle_score = 1.0 if is_valid_rectangle(corners) else 0.3

    # 基于位置稳定性（与上一次检测的距离）
    current_center = [sum([c[0] for c in corners]) // 4, sum([c[1] for c in corners]) // 4]
    distance = math.sqrt((current_center[0] - last_valid_center[0])**2 +
                        (current_center[1] - last_valid_center[1])**2)
    max_reasonable_distance = 30  # 最大合理移动距离
    distance_score = max(0.3, 1.0 - distance / max_reasonable_distance)

    # 综合置信度
    confidence = (area_score * 0.3 + aspect_score * 0.2 + angle_score * 0.3 + distance_score * 0.2)
    return min(1.0, confidence)

def smooth_position(new_center, last_center, smoothing_factor):
    """位置平滑滤波"""
    if last_center is None:
        return new_center

    smooth_x = int(last_center[0] * smoothing_factor + new_center[0] * (1 - smoothing_factor))
    smooth_y = int(last_center[1] * smoothing_factor + new_center[1] * (1 - smoothing_factor))

    return [smooth_x, smooth_y]

def simple_perspective_correction(corners):
    """
    轻量级透视矫正 - 专为OpenMV优化
    使用几何方法而非矩阵运算，保持高帧率
    """
    # 计算原始几何中心
    center_x = sum([c[0] for c in corners]) // 4
    center_y = sum([c[1] for c in corners]) // 4

    # 检测变形程度
    distortion = calculate_distortion_simple(corners)

    # 如果变形程度低，直接返回几何中心
    if distortion < MIN_DISTORTION_THRESHOLD:
        return center_x, center_y

    # 简化的角点排序：找到左上和右下角点
    # 左上角：x+y最小
    # 右下角：x+y最大
    min_sum = float('inf')
    max_sum = float('-inf')
    top_left = corners[0]
    bottom_right = corners[0]

    for corner in corners:
        corner_sum = corner[0] + corner[1]
        if corner_sum < min_sum:
            min_sum = corner_sum
            top_left = corner
        if corner_sum > max_sum:
            max_sum = corner_sum
            bottom_right = corner

    # 计算矩形的"理想"中心（基于对角线）
    ideal_center_x = (top_left[0] + bottom_right[0]) // 2
    ideal_center_y = (top_left[1] + bottom_right[1]) // 2

    # 计算矫正向量
    correction_x = ideal_center_x - center_x
    correction_y = ideal_center_y - center_y

    # 应用矫正强度
    corrected_x = center_x + int(correction_x * CORRECTION_STRENGTH)
    corrected_y = center_y + int(correction_y * CORRECTION_STRENGTH)

    # 边界检查
    corrected_x = max(0, min(corrected_x, 160))  # QQVGA宽度
    corrected_y = max(0, min(corrected_y, 120))  # QQVGA高度

    return corrected_x, corrected_y

def pack_data(x, y):
    """打包数据为帧头帧尾格式"""
    # 限制数据范围在-128到127之间（有符号字节）
    x = max(min(x, 127), -128)
    y = max(min(y, 127), -128)

    # 使用ustruct打包数据
    return ustruct.pack("bbbb", FRAME_HEADER, x, y, FRAME_FOOTER)

while True:
    clock.tick()
    img = sensor.snapshot()

    # 使用动态阈值检测旋转矩形
    rectangles = img.find_rects(threshold=int(detection_threshold))

    # 初始化偏差值
    bias_x = 0
    bias_y = 0
    detected = False
    best_rect = None
    best_confidence = 0.0

    # 评估所有检测到的矩形，选择置信度最高的
    for r in rectangles:
        area = r.w() * r.h()

        # 面积过滤
        if area < MIN_AREA or area > MAX_AREA:
            continue

        # 长宽比过滤
        aspect_ratio = max(r.w(), r.h()) / min(r.w(), r.h())
        if aspect_ratio < MIN_ASPECT_RATIO or aspect_ratio > MAX_ASPECT_RATIO:
            continue

        # 获取矩形角点
        corners = r.corners()

        # 验证是否是矩形（角度检查）
        if not is_valid_rectangle(corners):
            continue

        # 计算检测置信度
        confidence = calculate_detection_confidence(r, corners)

        # 选择置信度最高的矩形
        if confidence > best_confidence and confidence >= MIN_DETECTION_CONFIDENCE:
            best_confidence = confidence
            best_rect = r
            best_corners = corners

    # 处理最佳检测结果
    if best_rect is not None:
        detected = True
        r = best_rect
        corners = best_corners
        area = r.w() * r.h()
        aspect_ratio = max(r.w(), r.h()) / min(r.w(), r.h())

        # 更新稳定性计数
        stable_detection_count += 1

        # 绘制旋转矩形
        #img.draw_rectangle(r.rect(), color=(255, 0, 0), thickness=2)

        # 绘制角点和边
        for i in range(4):
            img.draw_circle(corners[i][0], corners[i][1], 3, color=(0, 0, 255), thickness=1)
            next_i = (i + 1) % 4
            img.draw_line(corners[i][0], corners[i][1],
                         corners[next_i][0], corners[next_i][1],
                         color=(0, 255, 0), thickness=2)

        # 计算原始几何中心
        original_center_x = sum([c[0] for c in corners]) // 4
        original_center_y = sum([c[1] for c in corners]) // 4

        # 透视矫正
        if ENABLE_PERSPECTIVE_CORRECTION:
            center_x, center_y = simple_perspective_correction(corners)
            # 绘制两个中心点用于对比（可选）
            if SHOW_CORRECTION_DEBUG:
                img.draw_circle(original_center_x, original_center_y, 2, color=(255, 0, 0), thickness=1)  # 红色：原始中心
            img.draw_cross(center_x, center_y, color=(255, 255, 0), size=8, thickness=2)  # 黄色：矫正中心
        else:
            center_x, center_y = original_center_x, original_center_y
            img.draw_cross(center_x, center_y, color=(255, 255, 0), size=8, thickness=2)

        # 应用位置平滑滤波
        if ENABLE_STABILITY_FILTER:
            smoothed_center = smooth_position([center_x, center_y], last_valid_center, SMOOTHING_FACTOR)
            center_x, center_y = smoothed_center[0], smoothed_center[1]

            # 更新历史数据
            last_valid_center[0] = center_x
            last_valid_center[1] = center_y
            last_valid_corners = corners
            last_detection_confidence = best_confidence

        # 计算偏差
        goal_bias_x = center_x - img.width() // 2
        goal_bias_y = center_y - img.height() // 2

        # 计算输出
        pid_out = goal_bias_x * abs(goal_bias_x) * 0.03 + goal_bias_x * 0.1 + (goal_bias_x - last_goal_bias_x) * 10
        last_goal_bias_x = goal_bias_x

        # 更新发送的偏差值
        bias_x = goal_bias_x
        bias_y = goal_bias_y

        # 计算发送给云台的定位数据
        bias_count_x = bias_count_x - int(pid_out)
        # 输出限幅
        if bias_count_x > 10000:
            bias_count_x = 10000
        if bias_count_x < -10000:
            bias_count_x = -10000

        # 自适应调整检测阈值
        if best_confidence > 0.8:
            detection_threshold = detection_threshold * (1 - THRESHOLD_ADAPT_SPEED) + DETECTION_THRESHOLD_BASE * 0.8 * THRESHOLD_ADAPT_SPEED
        elif best_confidence < 0.7:
            detection_threshold = detection_threshold * (1 - THRESHOLD_ADAPT_SPEED) + DETECTION_THRESHOLD_BASE * 1.2 * THRESHOLD_ADAPT_SPEED

        # 打印调试信息
        if ENABLE_PERSPECTIVE_CORRECTION and SHOW_CORRECTION_DEBUG:
            print("Detected: area={}, aspect={:.2f}, conf={:.2f}, orig_center=({},{}), corr_center=({},{}), bias_x={}, bias_y={}".format(
                area, aspect_ratio, best_confidence, original_center_x, original_center_y, center_x, center_y, bias_x, bias_y))
        else:
            print("Detected: area={}, aspect={:.2f}, conf={:.2f}, bias_x={}, bias_y={}".format(
                area, aspect_ratio, best_confidence, bias_x, bias_y))
    else:
        # 没有检测到有效矩形，重置稳定性计数
        stable_detection_count = 0

    # 如果没有检测到矩形，发送特定值（例如0x7F表示无效）
    if not detected:
        bias_x = 0x7F  # 127
        bias_y = 0x7F  # 127
        print("No rectangle detected")

    # 打包并发送数据
    data = pack_data(bias_x, bias_y)
    uart.write(data)

    # 打印FPS
    #print("FPS: %.1f" % clock.fps())
